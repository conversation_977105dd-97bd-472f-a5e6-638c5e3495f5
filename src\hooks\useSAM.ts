import { useState, useEffect, useCallback } from "react";
import * as ort from "onnxruntime-web";
import { SAMClick, SAMModelScale, SAMState, SAMModelInput } from "@/types/sam";
import { handleImageScale } from "@/lib/sam/scaleHelper";
import { onnxMaskToImage } from "@/lib/sam/maskUtils";
import { modelData } from "@/lib/sam/onnxModelAPI";
import { toast } from "sonner";

// Configuration for SAM
const SAM_CONFIG = {
    modelPath: "/model/sam_onnx_quantized_example.onnx",
    embeddingEndpoint:
        "https://rc19--sam-segmentation-api-sammodel-embed-image.modal.run",
};

export function useSAM() {
    const [samState, setSamState] = useState<SAMState>({
        model: null,
        tensor: null,
        modelScale: null,
        clicks: [],
        maskImg: null,
        isLoading: false,
        error: null,
        isPositiveClick: true,
        isMaskInverted: false,
    });

    const [isInitialized, setIsInitialized] = useState(false);

    // Initialize the ONNX model
    useEffect(() => {
        const initModel = async () => {
            try {
                setSamState((prev) => ({
                    ...prev,
                    isLoading: true,
                    error: null,
                }));

                try {
                    if (SAM_CONFIG.modelPath === undefined) return;
                    const URL: string = SAM_CONFIG.modelPath;
                    const model = await ort.InferenceSession.create(URL);
                    setSamState((prev) => ({
                        ...prev,
                        model,
                        isLoading: false,
                    }));
                    setIsInitialized(true);
                } catch (e) {
                    console.error("Error initializing SAM model:", e);
                }
                setIsInitialized(true);
            } catch (error) {
                console.error("Error initializing SAM model:", error);
                setSamState((prev) => ({
                    ...prev,
                    isLoading: false,
                    error:
                        error instanceof Error
                            ? error.message
                            : "Failed to load SAM model",
                }));
            }
        };
        initModel();
    }, []);

    // Get embedding from API (manual trigger)
    const generateEmbedding = async (
        imageDataUrl: string
    ): Promise<{ tensor: ort.Tensor; modelScale: SAMModelScale } | null> => {
        try {
            setSamState((prev) => ({ ...prev, isLoading: true, error: null }));

            // Convert data URL to base64
            const base64 = imageDataUrl.split(",")[1];

            const response = await fetch(SAM_CONFIG.embeddingEndpoint, {
                method: "POST",
                headers: { "Content-Type": "application/json" },
                body: JSON.stringify({ image: base64 }),
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data = await response.json();

            if (data.status === "success" && data.data && data.data.embedding) {
                const embeddingArray = new Float32Array(data.data.embedding);
                const tensor = new ort.Tensor(
                    "float32",
                    embeddingArray,
                    data.data.shape
                );

                // Create a temporary image to get dimensions and scale
                const img = new Image();
                img.crossOrigin = "anonymous";

                return new Promise((resolve, reject) => {
                    img.onload = () => {
                        const modelScale = handleImageScale(img);
                        setSamState((prev) => ({
                            ...prev,
                            tensor,
                            modelScale,
                            isLoading: false,
                            clicks: [],
                            maskImg: null,
                        }));
                        resolve({ tensor, modelScale });
                    };
                    img.onerror = () =>
                        reject(new Error("Failed to load image for scaling"));
                    img.src = imageDataUrl;
                });
            } else {
                throw new Error("Invalid response format from embedding API");
            }
        } catch (error) {
            console.error("Error getting embedding:", error);
            setSamState((prev) => ({
                ...prev,
                isLoading: false,
                error:
                    error instanceof Error
                        ? error.message
                        : "Failed to get embedding",
            }));
            toast.error("Failed to generate image embedding for SAM");
            return null;
        }
    };

    // Run ONNX inference
    const runONNX = useCallback(async () => {
        try {
            if (!samState.model || !samState.tensor || !samState.modelScale)
                return;

            if (samState.clicks.length === 0) {
                setSamState((prev) => ({ ...prev, maskImg: null }));
                return;
            }

            const modelCompatibleClicks: SAMModelInput[] = samState.clicks.map(
                (click: SAMClick) => ({
                    x: click.x,
                    y: click.y,
                    clickType: click.clickType,
                })
            );

            const feeds = modelData({
                clicks: modelCompatibleClicks,
                tensor: samState.tensor,
                modelScale: samState.modelScale,
            });

            if (!feeds) return;

            const results = await samState.model.run(feeds);
            const output = results[samState.model.outputNames[0]];

            let maskData = output.data as Float32Array;
            if (samState.isMaskInverted) {
                maskData = maskData.map((val) => 1 - val);
            }

            const maskImg = onnxMaskToImage(
                maskData,
                output.dims[2],
                output.dims[3]
            );
            setSamState((prev) => ({ ...prev, maskImg }));
        } catch (error) {
            console.error("Error running SAM inference:", error);
            setSamState((prev) => ({ ...prev, maskImg: null }));
        }
    }, [
        samState.model,
        samState.tensor,
        samState.modelScale,
        samState.clicks,
        samState.isMaskInverted,
    ]);

    // Run inference when clicks change or mask inversion toggles
    useEffect(() => {
        if (samState.clicks.length === 0) return;
        runONNX();
    }, [samState.clicks, samState.isMaskInverted, runONNX]);

    // Handle clicks with external history callback for undo/redo integration
    const handleClick = (x: number, y: number, onStateChange?: () => void) => {
        const newClick: SAMClick = {
            x,
            y,
            clickType: samState.isPositiveClick ? 1 : 0,
        };

        setSamState((prev) => ({
            ...prev,
            clicks: [...prev.clicks, newClick],
        }));

        // Trigger external history management after SAM processing
        if (onStateChange) {
            // Wait for both state update and ONNX processing to complete
            setTimeout(() => {
                onStateChange();
            }, 300);
        }
    };

    // Reset functionality - only manage SAM-specific state
    const reset = () => {
        setSamState((prev) => ({
            ...prev,
            clicks: [],
            maskImg: null,
            isMaskInverted: false,
        }));
    };

    // Reset only clicks but keep tensor and model scale
    const resetClicks = () => {
        setSamState((prev) => ({
            ...prev,
            clicks: [],
            maskImg: null,
            isMaskInverted: false,
        }));
    };

    // Restore SAM state for undo/redo functionality
    const restoreState = (clicks: SAMClick[], isMaskInverted: boolean = false) => {
        setSamState((prev) => ({
            ...prev,
            clicks,
            isMaskInverted,
        }));
    };

    // Complete reset including embedding tensor and mask state
    const resetAll = () => {
        setSamState(() => ({
            model: samState.model, // Keep the model loaded
            tensor: null,
            modelScale: null,
            clicks: [],
            maskImg: null,
            isLoading: false,
            error: null,
            isPositiveClick: true,
            isMaskInverted: false,
        }));
    };

    // Toggle positive/negative click
    const toggleClickType = () => {
        setSamState((prev) => ({
            ...prev,
            isPositiveClick: !prev.isPositiveClick,
        }));
    };

    // Invert mask
    const invertMask = () => {
        setSamState((prev) => ({
            ...prev,
            isMaskInverted: !prev.isMaskInverted,
        }));
    };

    // Generate cutout from mask
    const generateCutout = (
        originalImageElement: HTMLImageElement
    ): { annotatedMask: string; segmentedCutout: string } | null => {
        if (!samState.maskImg || !samState.modelScale) return null;

        try {
            // 1. Generate the black and white annotated mask
            const annotatedMaskCanvas = document.createElement("canvas");
            annotatedMaskCanvas.width = originalImageElement.width;
            annotatedMaskCanvas.height = originalImageElement.height;
            const annotatedMaskCtx = annotatedMaskCanvas.getContext("2d");
            if (!annotatedMaskCtx) return null;

            annotatedMaskCtx.fillStyle = "#000000";
            annotatedMaskCtx.fillRect(
                0,
                0,
                annotatedMaskCanvas.width,
                annotatedMaskCanvas.height
            );

            const tempMaskCanvas = document.createElement("canvas");
            tempMaskCanvas.width = samState.maskImg.naturalWidth;
            tempMaskCanvas.height = samState.maskImg.naturalHeight;
            const tempMaskCtx = tempMaskCanvas.getContext("2d");
            if (!tempMaskCtx) return null;

            tempMaskCtx.drawImage(samState.maskImg, 0, 0);
            const maskImageData = tempMaskCtx.getImageData(
                0,
                0,
                tempMaskCanvas.width,
                tempMaskCanvas.height
            );

            annotatedMaskCtx.fillStyle = "white";
            for (let y = 0; y < annotatedMaskCanvas.height; y++) {
                for (let x = 0; x < annotatedMaskCanvas.width; x++) {
                    const maskX = Math.floor(
                        (x / annotatedMaskCanvas.width) * maskImageData.width
                    );
                    const maskY = Math.floor(
                        (y / annotatedMaskCanvas.height) * maskImageData.height
                    );
                    const maskIdx = (maskY * maskImageData.width + maskX) * 4;
                    if (maskImageData.data[maskIdx + 3] > 128) {
                        annotatedMaskCtx.fillRect(x, y, 1, 1);
                    }
                }
            }

            // 2. Generate the segmented cutout
            const segmentedCanvas = document.createElement("canvas");
            segmentedCanvas.width = originalImageElement.width;
            segmentedCanvas.height = originalImageElement.height;
            const segmentedCtx = segmentedCanvas.getContext("2d");
            if (!segmentedCtx) return null;

            segmentedCtx.drawImage(
                originalImageElement,
                0,
                0,
                originalImageElement.width,
                originalImageElement.height
            );
            const originalImageData = segmentedCtx.getImageData(
                0,
                0,
                originalImageElement.width,
                originalImageElement.height
            );
            const newImageData = segmentedCtx.createImageData(
                originalImageElement.width,
                originalImageElement.height
            );

            for (let i = 0; i < originalImageData.data.length; i += 4) {
                const x = (i / 4) % originalImageElement.width;
                const y = Math.floor(i / 4 / originalImageElement.width);
                const maskX = Math.floor(
                    (x / originalImageElement.width) * maskImageData.width
                );
                const maskY = Math.floor(
                    (y / originalImageElement.height) * maskImageData.height
                );
                const maskIdx = (maskY * maskImageData.width + maskX) * 4;

                if (maskImageData.data[maskIdx + 3] > 128) {
                    newImageData.data[i] = originalImageData.data[i];
                    newImageData.data[i + 1] = originalImageData.data[i + 1];
                    newImageData.data[i + 2] = originalImageData.data[i + 2];
                    newImageData.data[i + 3] = originalImageData.data[i + 3];
                } else {
                    newImageData.data[i + 3] = 0; // Make transparent
                }
            }

            segmentedCtx.putImageData(newImageData, 0, 0);

            return {
                annotatedMask: annotatedMaskCanvas.toDataURL(),
                segmentedCutout: segmentedCanvas.toDataURL(),
            };
        } catch (error) {
            console.error("Error generating cutout:", error);
            return null;
        }
    };
    return {
        samState,
        isInitialized,
        generateEmbedding,
        handleClick,
        reset,
        resetClicks,
        resetAll,
        restoreState,
        toggleClickType,
        invertMask,
        generateCutout,
    };
}
